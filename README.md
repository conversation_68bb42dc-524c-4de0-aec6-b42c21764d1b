# 恆生指數期貨5分鐘監察圖

這是一個基於HTML和JavaScript的恆生指數期貨實時監控程式，使用FUTU OpenD API獲取數據。

## 功能特點

- 🔄 實時連接FUTU OpenD
- 📊 5分鐘K線圖表顯示
- 💰 實時價格監控
- 📈 漲跌幅顯示
- 🔔 自動數據更新
- 📱 響應式設計

## 系統要求

### 必要條件
1. **FUTU OpenD程式** - 必須已安裝並運行
2. **FUTU賬戶** - 已登錄的FUTU賬戶
3. **現代瀏覽器** - 支持WebSocket和Chart.js

### FUTU OpenD設置
1. 確保FUTU OpenD已啟動並運行在默認端口 `33333`
2. 如果設置了API密鑰，請在連接時輸入
3. 確保已登錄FUTU賬戶並有恆生指數期貨的數據權限

## 使用方法

### 1. 文件準備
確保以下文件在同一目錄下：
```
├── hsi_futures_monitor.html    # 主程式文件
├── FTAPI4JS_9.3.5308/         # FUTU API文件夾
│   └── src/
│       ├── base.js
│       ├── main.js
│       └── proto.js
└── README.md                   # 說明文檔
```

### 2. 啟動程式
1. 確保FUTU OpenD正在運行
2. 用瀏覽器打開 `hsi_futures_monitor.html`
3. 在連接設定面板中：
   - 服務器地址：通常是 `127.0.0.1`
   - 端口：通常是 `33333`
   - API密鑰：如果FUTU OpenD設置了密鑰，請輸入
4. 點擊「連接」按鈕

### 3. 監控數據
連接成功後，程式將：
- 自動訂閱恆生指數期貨數據
- 顯示實時價格和漲跌幅
- 更新5分鐘K線圖表
- 顯示成交量信息

## 界面說明

### 連接面板
- **服務器地址**：FUTU OpenD的IP地址
- **端口**：FUTU OpenD的WebSocket端口
- **API密鑰**：可選，如果OpenD設置了密鑰驗證
- **連接狀態**：顯示當前連接狀態

### 信息面板
- **當前價格**：最新的期貨價格
- **漲跌**：相對於前一個價格的變化
- **漲跌幅**：百分比變化
- **成交量**：當前成交量
- **最後更新**：數據最後更新時間

### 圖表區域
- 顯示5分鐘K線圖
- 支持縮放和平移
- 實時更新數據點

## 技術說明

### 使用的技術
- **Chart.js** - 圖表繪製
- **FUTU OpenD API** - 數據獲取
- **WebSocket** - 實時通信
- **HTML5/CSS3/JavaScript** - 前端界面

### 數據更新機制
1. **初始數據**：連接後獲取最近100根5分鐘K線
2. **實時推送**：訂閱基本報價和K線更新
3. **圖表更新**：新數據自動添加到圖表
4. **數據限制**：保持最多200個數據點以優化性能

### 恆生指數期貨代碼
程式默認監控恆生指數主力合約 (`HSImain`)，這是最活躍的合約。

## 故障排除

### 常見問題

**1. 連接失敗**
- 檢查FUTU OpenD是否正在運行
- 確認端口號是否正確（默認33333）
- 檢查防火牆設置

**2. 無數據顯示**
- 確認已登錄FUTU賬戶
- 檢查是否有恆生指數期貨的數據權限
- 確認市場是否開放

**3. 圖表不更新**
- 檢查瀏覽器控制台是否有錯誤
- 確認WebSocket連接是否穩定
- 刷新頁面重新連接

**4. API密鑰錯誤**
- 檢查FUTU OpenD的密鑰設置
- 確認輸入的密鑰是否正確
- 如果不確定，可以留空嘗試

### 調試模式
打開瀏覽器開發者工具（F12）查看控制台日誌，可以看到：
- 連接狀態信息
- 數據接收日誌
- 錯誤信息

## 注意事項

### 重要提醒
1. **數據權限**：需要有效的FUTU賬戶和相應的市場數據權限
2. **網絡連接**：確保網絡連接穩定
3. **瀏覽器兼容性**：建議使用Chrome、Firefox或Edge最新版本
4. **資源使用**：長時間運行可能消耗較多內存，建議定期刷新

### 免責聲明
- 本程式僅供學習和參考使用
- 投資有風險，請謹慎決策
- 數據延遲和準確性請以官方為準

## 擴展功能

可以考慮添加的功能：
- 多個期貨合約監控
- 技術指標計算（MA、MACD等）
- 價格警報功能
- 歷史數據回放
- 數據導出功能

## 聯繫支持

如果遇到問題，請：
1. 檢查FUTU OpenD官方文檔
2. 確認API使用權限
3. 查看瀏覽器控制台錯誤信息

---

**版本**: 1.0  
**更新日期**: 2025-01-07  
**兼容性**: FUTU OpenD API v9.3.5308+
