<!DOCTYPE html>
<html lang="zh-HK">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FUTU 連接測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-panel {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
        }
        .status.connected { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.connecting { background: #fff3cd; color: #856404; }
        .log {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>FUTU OpenD 連接測試</h1>
    
    <div class="test-panel">
        <h3>連接設置</h3>
        <p>服務器: <input type="text" id="serverAddr" value="127.0.0.1" style="width: 150px;"></p>
        <p>端口: <input type="number" id="serverPort" value="33333" style="width: 100px;"></p>
        <p>密鑰: <input type="text" id="apiKey" value="9938a4152c0ad386" style="width: 200px;"></p>
        
        <button class="btn btn-primary" onclick="testConnection()">測試連接</button>
        <button class="btn btn-danger" onclick="disconnect()">斷開連接</button>
        <button class="btn btn-success" onclick="clearLog()">清除日誌</button>
        
        <div id="status" class="status">未連接</div>
    </div>
    
    <div class="test-panel">
        <h3>連接日誌</h3>
        <div id="log" class="log"></div>
    </div>

    <script>
        let websocket = null;
        let isConnected = false;

        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function updateStatus(status, message) {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${status}`;
            statusDiv.textContent = message;
        }

        function testConnection() {
            if (isConnected) {
                log('已經連接中');
                return;
            }

            const serverAddr = document.getElementById('serverAddr').value;
            const serverPort = document.getElementById('serverPort').value;
            const apiKey = document.getElementById('apiKey').value;

            log(`開始連接到 ${serverAddr}:${serverPort}`);
            log(`使用密鑰: ${apiKey}`);
            
            updateStatus('connecting', '正在連接...');

            try {
                const protocol = 'ws'; // 不使用SSL
                const url = `${protocol}://${serverAddr}:${serverPort}`;
                
                log(`WebSocket URL: ${url}`);
                websocket = new WebSocket(url);

                websocket.onopen = function(event) {
                    log('WebSocket 連接已建立');
                    isConnected = true;
                    updateStatus('connected', '連接成功');
                    
                    // 發送初始化消息
                    const initMessage = {
                        type: 'init',
                        key: apiKey,
                        programmingLanguage: 'JavaScript'
                    };
                    
                    websocket.send(JSON.stringify(initMessage));
                    log('已發送初始化消息');
                };

                websocket.onerror = function(error) {
                    log('WebSocket 錯誤: ' + error);
                    updateStatus('error', '連接錯誤');
                    isConnected = false;
                };

                websocket.onclose = function(event) {
                    log(`WebSocket 連接已關閉 (代碼: ${event.code}, 原因: ${event.reason})`);
                    updateStatus('error', '連接已關閉');
                    isConnected = false;
                };

                websocket.onmessage = function(event) {
                    log('收到消息: ' + event.data);
                };

            } catch (error) {
                log('連接失敗: ' + error.message);
                updateStatus('error', '連接失敗: ' + error.message);
            }
        }

        function disconnect() {
            if (websocket) {
                websocket.close();
                websocket = null;
            }
            isConnected = false;
            updateStatus('error', '已斷開連接');
            log('手動斷開連接');
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // 頁面載入時的初始化
        log('頁面已載入，準備測試連接');
    </script>
</body>
</html>
