<!DOCTYPE html>
<html lang="zh-HK">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>恆生指數期貨5分鐘監察圖</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-chart-financial"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
            color: #333;
        }
        .connection-panel {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .connection-row {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            gap: 10px;
        }
        .connection-row label {
            min-width: 80px;
            font-weight: bold;
        }
        .connection-row input {
            padding: 5px 10px;
            border: 1px solid #ddd;
            border-radius: 3px;
            flex: 1;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background-color: #0056b3;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .btn-danger:hover {
            background-color: #c82333;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.error {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .chart-container {
            position: relative;
            height: 500px;
            margin-top: 20px;
        }
        .info-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .info-card {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .info-card h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .info-card .value {
            font-size: 1.2em;
            font-weight: bold;
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>恆生指數期貨5分鐘監察圖</h1>
            <p>實時監控恆生指數期貨價格走勢</p>
        </div>

        <div class="connection-panel">
            <h3>FUTU 賬戶登入</h3>
            <div class="connection-row">
                <label>用戶名:</label>
                <input type="text" id="username" placeholder="FUTU賬戶用戶名">
                <label>密碼:</label>
                <input type="password" id="password" placeholder="FUTU賬戶密碼">
            </div>
            <div class="connection-row">
                <label>服務器:</label>
                <input type="text" id="serverAddr" value="127.0.0.1" placeholder="服務器地址">
                <label>端口:</label>
                <input type="number" id="serverPort" value="33333" placeholder="端口號">
            </div>
            <div class="connection-row">
                <label>密鑰:</label>
                <input type="text" id="apiKey" placeholder="API密鑰 (可選)">
                <button class="btn btn-primary" id="connectBtn" onclick="toggleConnection()">登入並連接</button>
            </div>
            <div id="connectionStatus" class="status disconnected">未連接</div>
            <div id="loginStatus" class="status disconnected" style="display:none;">未登入</div>
        </div>

        <div class="info-panel">
            <div class="info-card">
                <h4>當前價格</h4>
                <div class="value" id="currentPrice">--</div>
            </div>
            <div class="info-card">
                <h4>漲跌</h4>
                <div class="value" id="priceChange">--</div>
            </div>
            <div class="info-card">
                <h4>漲跌幅</h4>
                <div class="value" id="changePercent">--</div>
            </div>
            <div class="info-card">
                <h4>成交量</h4>
                <div class="value" id="volume">--</div>
            </div>
            <div class="info-card">
                <h4>最後更新</h4>
                <div class="value" id="lastUpdate">--</div>
            </div>
        </div>

        <div class="chart-container">
            <canvas id="priceChart"></canvas>
        </div>
    </div>

    <!-- 引入必要的依賴 -->
    <script src="https://cdn.jsdelivr.net/npm/protobufjs@6.11.3/dist/protobuf.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/long@4.0.0/dist/long.min.js"></script>

    <!-- 引入配置文件 -->
    <script src="config.js"></script>

    <script>
        // 模擬FUTU API的基本結構 (簡化版本用於演示)
        class FutuWebSocket {
            constructor() {
                this.websocket = null;
                this.isConnected = false;
                this.onlogin = null;
                this.onPush = null;
                this.callbacks = new Map();
                this.messageId = 1;
            }

            start(host, port, ssl, key) {
                return new Promise((resolve, reject) => {
                    try {
                        const protocol = ssl ? 'wss' : 'ws';
                        const url = `${protocol}://${host}:${port}`;

                        this.websocket = new WebSocket(url);

                        this.websocket.onopen = () => {
                            console.log('WebSocket連接已建立');
                            this.isConnected = true;
                            if (this.onlogin) {
                                this.onlogin(true, '連接成功');
                            }
                            resolve();
                        };

                        this.websocket.onmessage = (event) => {
                            this.handleMessage(event.data);
                        };

                        this.websocket.onerror = (error) => {
                            console.error('WebSocket錯誤:', error);
                            if (this.onlogin) {
                                this.onlogin(false, '連接失敗');
                            }
                            reject(error);
                        };

                        this.websocket.onclose = () => {
                            console.log('WebSocket連接已關閉');
                            this.isConnected = false;
                        };

                    } catch (error) {
                        reject(error);
                    }
                });
            }

            stop() {
                if (this.websocket) {
                    this.websocket.close();
                    this.websocket = null;
                    this.isConnected = false;
                }
            }

            // 模擬訂閱功能
            Sub(request) {
                return new Promise((resolve, reject) => {
                    if (!this.isConnected) {
                        reject(new Error('未連接'));
                        return;
                    }

                    // 模擬成功響應
                    setTimeout(() => {
                        resolve({ retType: 0, retMsg: '訂閱成功' });
                    }, 100);
                });
            }

            // 模擬獲取K線數據
            GetKL(request) {
                return new Promise((resolve, reject) => {
                    if (!this.isConnected) {
                        reject(new Error('未連接'));
                        return;
                    }

                    // 生成模擬K線數據
                    const klList = this.generateMockKLineData();

                    setTimeout(() => {
                        resolve({
                            retType: 0,
                            retMsg: '成功',
                            s2c: {
                                klList: klList
                            }
                        });
                    }, 200);
                });
            }

            // 生成模擬K線數據
            generateMockKLineData() {
                const klList = [];
                const now = Date.now();
                let currentPrice = 25000; // 恆指期貨基準價格

                for (let i = 99; i >= 0; i--) {
                    const timestamp = Math.floor((now - i * 5 * 60 * 1000) / 1000); // 5分鐘間隔

                    // 生成開盤價（基於前一個收盤價）
                    const openPrice = currentPrice + (Math.random() - 0.5) * 30;

                    // 生成收盤價
                    const closePrice = openPrice + (Math.random() - 0.5) * 80;

                    // 生成最高價和最低價
                    const highPrice = Math.max(openPrice, closePrice) + Math.random() * 40;
                    const lowPrice = Math.min(openPrice, closePrice) - Math.random() * 40;

                    klList.push({
                        timestamp: timestamp,
                        openPrice: Math.round(openPrice * 100) / 100,
                        closePrice: Math.round(closePrice * 100) / 100,
                        highPrice: Math.round(highPrice * 100) / 100,
                        lowPrice: Math.round(lowPrice * 100) / 100,
                        volume: Math.floor(Math.random() * 10000) + 1000
                    });

                    // 更新當前價格為收盤價，用於下一根K線
                    currentPrice = closePrice;
                }

                return klList;
            }

            handleMessage(data) {
                // 處理接收到的消息
                if (this.onPush) {
                    // 模擬推送數據
                    this.onPush(3005, {
                        s2c: {
                            basicQotList: [{
                                curPrice: 25000 + (Math.random() - 0.5) * 200,
                                volume: Math.floor(Math.random() * 10000) + 1000
                            }]
                        }
                    });
                }
            }

            // 模擬定期推送數據
            startMockPush() {
                if (this.pushInterval) {
                    clearInterval(this.pushInterval);
                }

                this.pushInterval = setInterval(() => {
                    if (this.isConnected && this.onPush) {
                        // 推送基本報價
                        this.onPush(3005, {
                            s2c: {
                                basicQotList: [{
                                    curPrice: 25000 + (Math.random() - 0.5) * 200,
                                    volume: Math.floor(Math.random() * 10000) + 1000
                                }]
                            }
                        });

                        // 偶爾推送新的K線數據
                        if (Math.random() < 0.1) { // 10%機率
                            const now = Math.floor(Date.now() / 1000);
                            this.onPush(3007, {
                                s2c: {
                                    klList: [{
                                        timestamp: now,
                                        openPrice: 25000 + (Math.random() - 0.5) * 100,
                                        closePrice: 25000 + (Math.random() - 0.5) * 100,
                                        highPrice: 25000 + Math.random() * 150,
                                        lowPrice: 25000 - Math.random() * 150,
                                        volume: Math.floor(Math.random() * 10000) + 1000
                                    }]
                                }
                            });
                        }
                    }
                }, 2000); // 每2秒推送一次
            }
        }

        // 使用FutuWebSocket替代原來的ftWebsocket
        window.ftWebsocket = FutuWebSocket;
    </script>

    <script>
        // 全局變量
        let ftWebsocketInstance = null;
        let priceChart = null;
        let isConnected = false;
        let isLoggedIn = false;
        let chartData = [];
        let lastPrice = 0;
        let userCredentials = null;

        // 恆生指數期貨代碼 (主力合約)
        const HSI_FUTURES = {
            market: 1, // 香港市場
            code: "HSImain" // 恆生指數主力合約
        };

        // 初始化K線圖表
        function initChart() {
            const ctx = document.getElementById('priceChart').getContext('2d');
            priceChart = new Chart(ctx, {
                type: 'candlestick',
                data: {
                    datasets: [{
                        label: '恆生指數期貨 5分鐘K線',
                        data: [],
                        borderColor: {
                            up: '#26a69a',    // 上漲蠟燭邊框色
                            down: '#ef5350',  // 下跌蠟燭邊框色
                            unchanged: '#999' // 不變蠟燭邊框色
                        },
                        backgroundColor: {
                            up: '#26a69a',    // 上漲蠟燭填充色
                            down: '#ef5350',  // 下跌蠟燭填充色
                            unchanged: '#999' // 不變蠟燭填充色
                        },
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            type: 'time',
                            time: {
                                unit: 'minute',
                                stepSize: 5,
                                displayFormats: {
                                    minute: 'HH:mm'
                                }
                            },
                            title: {
                                display: true,
                                text: '時間'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: '價格'
                            },
                            ticks: {
                                callback: function(value) {
                                    return value.toLocaleString();
                                }
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false,
                            callbacks: {
                                title: function(context) {
                                    return new Date(context[0].parsed.x).toLocaleString();
                                },
                                label: function(context) {
                                    const data = context.parsed;
                                    return [
                                        `開盤: ${data.o.toLocaleString()}`,
                                        `最高: ${data.h.toLocaleString()}`,
                                        `最低: ${data.l.toLocaleString()}`,
                                        `收盤: ${data.c.toLocaleString()}`
                                    ];
                                }
                            }
                        }
                    },
                    interaction: {
                        mode: 'nearest',
                        axis: 'x',
                        intersect: false
                    }
                }
            });
        }

        // 更新連接狀態
        function updateConnectionStatus(status, message) {
            const statusElement = document.getElementById('connectionStatus');
            const connectBtn = document.getElementById('connectBtn');

            statusElement.className = `status ${status}`;
            statusElement.textContent = message;

            if (status === 'connected') {
                connectBtn.textContent = '斷開連接';
                connectBtn.className = 'btn btn-danger';
                isConnected = true;
            } else {
                connectBtn.textContent = isLoggedIn ? '連接' : '登入並連接';
                connectBtn.className = 'btn btn-primary';
                isConnected = false;
            }
        }

        // 更新登入狀態
        function updateLoginStatus(status, message) {
            const loginStatusElement = document.getElementById('loginStatus');
            loginStatusElement.style.display = 'block';
            loginStatusElement.className = `status ${status}`;
            loginStatusElement.textContent = message;

            if (status === 'connected') {
                isLoggedIn = true;
            } else {
                isLoggedIn = false;
            }
        }

        // 驗證登入信息
        function validateLogin() {
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();

            if (!username || !password) {
                updateLoginStatus('error', '請輸入用戶名和密碼');
                return false;
            }

            // 保存用戶憑證
            userCredentials = { username, password };
            return true;
        }

        // 模擬登入驗證
        function performLogin() {
            return new Promise((resolve, reject) => {
                updateLoginStatus('connecting', '正在驗證登入信息...');

                // 模擬登入過程
                setTimeout(() => {
                    if (userCredentials && userCredentials.username && userCredentials.password) {
                        updateLoginStatus('connected', `已登入: ${userCredentials.username}`);
                        resolve(true);
                    } else {
                        updateLoginStatus('error', '登入失敗: 用戶名或密碼錯誤');
                        reject(new Error('登入失敗'));
                    }
                }, 1500);
            });
        }

        // 切換連接狀態
        function toggleConnection() {
            if (isConnected) {
                disconnect();
            } else {
                connect();
            }
        }

        // 連接到FUTU OpenD
        async function connect() {
            // 如果還未登入，先進行登入驗證
            if (!isLoggedIn) {
                if (!validateLogin()) {
                    return;
                }

                try {
                    await performLogin();
                } catch (error) {
                    console.error('登入失敗:', error);
                    return;
                }
            }

            const serverAddr = document.getElementById('serverAddr').value;
            const serverPort = parseInt(document.getElementById('serverPort').value);
            const apiKey = document.getElementById('apiKey').value;

            try {
                updateConnectionStatus('connecting', '正在連接FUTU OpenD...');

                // 創建WebSocket連接
                ftWebsocketInstance = new ftWebsocket();

                // 設置回調函數
                ftWebsocketInstance.onlogin = onLogin;
                ftWebsocketInstance.onPush = onPush;

                // 開始連接 (注意：這裡使用模擬連接，實際環境中會連接到真實的FUTU OpenD)
                ftWebsocketInstance.start(serverAddr, serverPort, false, apiKey || null)
                    .then(() => {
                        console.log('連接建立成功');
                    })
                    .catch(error => {
                        console.error('連接失敗:', error);
                        updateConnectionStatus('error', '連接失敗: ' + error.message);
                    });

            } catch (error) {
                console.error('連接錯誤:', error);
                updateConnectionStatus('error', '連接失敗: ' + error.message);
            }
        }

        // 斷開連接
        function disconnect() {
            if (ftWebsocketInstance) {
                ftWebsocketInstance.stop();
                ftWebsocketInstance = null;
            }
            updateConnectionStatus('disconnected', '已斷開連接');
        }

        // 登錄回調
        function onLogin(success, message) {
            if (success) {
                updateConnectionStatus('connected', '已連接到FUTU OpenD (模擬模式)');
                subscribeToHSIFutures();
                getInitialKLineData();
                // 開始模擬數據推送
                if (ftWebsocketInstance && ftWebsocketInstance.startMockPush) {
                    ftWebsocketInstance.startMockPush();
                }
            } else {
                updateConnectionStatus('error', '登錄失敗: ' + (message || '未知錯誤'));
            }
        }

        // 訂閱恆生指數期貨數據
        function subscribeToHSIFutures() {
            const subRequest = {
                c2s: {
                    securityList: [HSI_FUTURES],
                    subTypeList: [1, 6], // 基本報價和5分鐘K線
                    isSubOrUnSub: true,
                    isRegOrUnRegPush: true
                }
            };

            ftWebsocketInstance.Sub(subRequest)
                .then(response => {
                    console.log('訂閱成功:', response);
                })
                .catch(error => {
                    console.error('訂閱失敗:', error);
                    updateConnectionStatus('error', '訂閱失敗: ' + (error.retMsg || error.message));
                });
        }

        // 獲取初始K線數據
        function getInitialKLineData() {
            const klRequest = {
                c2s: {
                    rehabType: 1, // 前復權
                    klType: 7, // 5分鐘K線
                    security: HSI_FUTURES,
                    reqNum: 100 // 請求最近100根K線
                }
            };

            ftWebsocketInstance.GetKL(klRequest)
                .then(response => {
                    if (response.retType === 0 && response.s2c.klList) {
                        processKLineData(response.s2c.klList);
                    }
                })
                .catch(error => {
                    console.error('獲取K線數據失敗:', error);
                });
        }

        // 處理K線數據
        function processKLineData(klList) {
            chartData = klList.map(kl => ({
                x: new Date(kl.timestamp * 1000),
                o: kl.openPrice,   // 開盤價
                h: kl.highPrice,   // 最高價
                l: kl.lowPrice,    // 最低價
                c: kl.closePrice   // 收盤價
            }));

            // 更新圖表
            priceChart.data.datasets[0].data = chartData;
            priceChart.update();

            // 更新當前價格信息
            if (klList.length > 0) {
                const latestKL = klList[klList.length - 1];
                updatePriceInfo(latestKL);
            }
        }

        // 推送數據回調
        function onPush(cmd, data) {
            console.log('收到推送數據:', cmd, data);
            
            // 處理基本報價推送 (cmd: 3005)
            if (cmd === 3005 && data.s2c && data.s2c.basicQotList) {
                const basicQot = data.s2c.basicQotList[0];
                if (basicQot) {
                    updateBasicQuote(basicQot);
                }
            }
            
            // 處理K線推送 (cmd: 3007)
            if (cmd === 3007 && data.s2c && data.s2c.klList) {
                const newKL = data.s2c.klList[0];
                if (newKL) {
                    updateKLineData(newKL);
                }
            }
        }

        // 更新基本報價信息
        function updateBasicQuote(basicQot) {
            const currentPrice = basicQot.curPrice;
            const priceChange = currentPrice - lastPrice;
            const changePercent = lastPrice > 0 ? ((priceChange / lastPrice) * 100) : 0;

            document.getElementById('currentPrice').textContent = currentPrice.toFixed(2);
            document.getElementById('priceChange').textContent = (priceChange >= 0 ? '+' : '') + priceChange.toFixed(2);
            document.getElementById('changePercent').textContent = (changePercent >= 0 ? '+' : '') + changePercent.toFixed(2) + '%';
            document.getElementById('volume').textContent = basicQot.volume || '--';
            document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString();

            // 更新顏色
            const changeElement = document.getElementById('priceChange');
            const percentElement = document.getElementById('changePercent');
            const color = priceChange >= 0 ? '#28a745' : '#dc3545';
            changeElement.style.color = color;
            percentElement.style.color = color;

            lastPrice = currentPrice;
        }

        // 更新K線數據
        function updateKLineData(newKL) {
            const newDataPoint = {
                x: new Date(newKL.timestamp * 1000),
                o: newKL.openPrice,   // 開盤價
                h: newKL.highPrice,   // 最高價
                l: newKL.lowPrice,    // 最低價
                c: newKL.closePrice   // 收盤價
            };

            // 檢查是否是新的K線還是更新現有K線
            if (chartData.length > 0) {
                const lastDataPoint = chartData[chartData.length - 1];
                if (lastDataPoint.x.getTime() === newDataPoint.x.getTime()) {
                    // 更新現有K線
                    chartData[chartData.length - 1] = newDataPoint;
                } else {
                    // 添加新K線
                    chartData.push(newDataPoint);

                    // 保持最多200個數據點
                    if (chartData.length > 200) {
                        chartData.shift();
                    }
                }
            } else {
                chartData.push(newDataPoint);
            }

            // 更新圖表
            priceChart.data.datasets[0].data = chartData;
            priceChart.update('none'); // 不使用動畫以提高性能

            // 更新價格信息
            updatePriceInfo(newKL);
        }

        // 更新價格信息
        function updatePriceInfo(klData) {
            document.getElementById('currentPrice').textContent = klData.closePrice.toFixed(2);
            document.getElementById('lastUpdate').textContent = new Date(klData.timestamp * 1000).toLocaleTimeString();
        }

        // 頁面加載完成後初始化
        document.addEventListener('DOMContentLoaded', function() {
            initChart();
            updateConnectionStatus('disconnected', '未連接');

            // 從配置文件加載默認值
            if (typeof FutuConfig !== 'undefined') {
                document.getElementById('serverAddr').value = FutuConfig.server.host;
                document.getElementById('serverPort').value = FutuConfig.server.port;
                if (FutuConfig.server.apiKey) {
                    document.getElementById('apiKey').value = FutuConfig.server.apiKey;
                }
            }

            // 嘗試從本地存儲加載配置
            if (typeof FutuConfigUtils !== 'undefined') {
                FutuConfigUtils.load();
            }
        });

        // 頁面卸載時清理資源
        window.addEventListener('beforeunload', function() {
            if (ftWebsocketInstance) {
                ftWebsocketInstance.stop();
            }
        });
    </script>
</body>
</html>
