<!DOCTYPE html>
<html lang="zh-HK">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>恆生指數期貨5分鐘監察圖 - 演示模式</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
            color: #333;
        }
        .demo-notice {
            background-color: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border: 1px solid #ffeaa7;
        }
        .demo-notice h4 {
            margin: 0 0 10px 0;
        }
        .connection-panel {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            margin: 5px;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.demo {
            background-color: #cce5ff;
            color: #004085;
            border: 1px solid #99d6ff;
        }
        .chart-container {
            position: relative;
            height: 500px;
            margin-top: 20px;
        }
        .info-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .info-card {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .info-card h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .info-card .value {
            font-size: 1.2em;
            font-weight: bold;
            color: #007bff;
        }
        .positive {
            color: #28a745 !important;
        }
        .negative {
            color: #dc3545 !important;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>恆生指數期貨5分鐘監察圖</h1>
            <p>實時監控恆生指數期貨價格走勢 - 演示模式</p>
        </div>

        <div class="demo-notice">
            <h4>🎯 演示模式說明</h4>
            <p>這是一個演示版本，使用模擬數據展示程式功能。在實際使用中，請確保：</p>
            <ul>
                <li>FUTU OpenD程式已安裝並運行</li>
                <li>已登錄FUTU賬戶並有相應的數據權限</li>
                <li>使用真實的API連接參數</li>
            </ul>
        </div>

        <div class="connection-panel">
            <h3>演示控制</h3>
            <button class="btn btn-success" onclick="startDemo()">開始演示</button>
            <button class="btn btn-danger" onclick="stopDemo()">停止演示</button>
            <button class="btn btn-primary" onclick="resetDemo()">重置數據</button>
            <div id="demoStatus" class="status demo">點擊"開始演示"查看效果</div>
        </div>

        <div class="info-panel">
            <div class="info-card">
                <h4>當前價格</h4>
                <div class="value" id="currentPrice">25,000.00</div>
            </div>
            <div class="info-card">
                <h4>漲跌</h4>
                <div class="value" id="priceChange">+0.00</div>
            </div>
            <div class="info-card">
                <h4>漲跌幅</h4>
                <div class="value" id="changePercent">+0.00%</div>
            </div>
            <div class="info-card">
                <h4>成交量</h4>
                <div class="value" id="volume">5,280</div>
            </div>
            <div class="info-card">
                <h4>最後更新</h4>
                <div class="value" id="lastUpdate">--:--:--</div>
            </div>
        </div>

        <div class="chart-container">
            <canvas id="priceChart"></canvas>
        </div>
    </div>

    <script>
        // 全局變量
        let priceChart = null;
        let demoInterval = null;
        let isRunning = false;
        let chartData = [];
        let basePrice = 25000;
        let currentPrice = basePrice;
        let previousPrice = basePrice;

        // 初始化圖表
        function initChart() {
            const ctx = document.getElementById('priceChart').getContext('2d');
            priceChart = new Chart(ctx, {
                type: 'line',
                data: {
                    datasets: [{
                        label: '恆生指數期貨 (演示數據)',
                        data: [],
                        borderColor: '#007bff',
                        backgroundColor: 'rgba(0, 123, 255, 0.1)',
                        borderWidth: 2,
                        fill: false,
                        tension: 0.1,
                        pointRadius: 3,
                        pointHoverRadius: 5
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            type: 'time',
                            time: {
                                unit: 'minute',
                                displayFormats: {
                                    minute: 'HH:mm'
                                }
                            },
                            title: {
                                display: true,
                                text: '時間'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: '價格'
                            },
                            ticks: {
                                callback: function(value) {
                                    return value.toLocaleString();
                                }
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false,
                            callbacks: {
                                label: function(context) {
                                    return '價格: ' + context.parsed.y.toLocaleString();
                                }
                            }
                        }
                    },
                    interaction: {
                        mode: 'nearest',
                        axis: 'x',
                        intersect: false
                    }
                }
            });
        }

        // 生成初始數據
        function generateInitialData() {
            chartData = [];
            const now = new Date();
            
            for (let i = 99; i >= 0; i--) {
                const time = new Date(now.getTime() - i * 5 * 60 * 1000); // 5分鐘間隔
                const price = basePrice + (Math.random() - 0.5) * 500; // 隨機波動
                
                chartData.push({
                    x: time,
                    y: price
                });
            }
            
            if (chartData.length > 0) {
                currentPrice = chartData[chartData.length - 1].y;
                previousPrice = chartData.length > 1 ? chartData[chartData.length - 2].y : currentPrice;
            }
            
            updateChart();
            updatePriceInfo();
        }

        // 更新圖表
        function updateChart() {
            priceChart.data.datasets[0].data = chartData;
            priceChart.update('none');
        }

        // 更新價格信息
        function updatePriceInfo() {
            const change = currentPrice - previousPrice;
            const changePercent = previousPrice > 0 ? ((change / previousPrice) * 100) : 0;
            const volume = Math.floor(Math.random() * 10000) + 1000;

            document.getElementById('currentPrice').textContent = currentPrice.toLocaleString('zh-HK', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });

            const changeElement = document.getElementById('priceChange');
            const percentElement = document.getElementById('changePercent');
            
            changeElement.textContent = (change >= 0 ? '+' : '') + change.toFixed(2);
            percentElement.textContent = (changePercent >= 0 ? '+' : '') + changePercent.toFixed(2) + '%';
            
            // 設置顏色
            const colorClass = change >= 0 ? 'positive' : 'negative';
            changeElement.className = 'value ' + colorClass;
            percentElement.className = 'value ' + colorClass;

            document.getElementById('volume').textContent = volume.toLocaleString();
            document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString();
        }

        // 生成新的價格數據
        function generateNewPrice() {
            // 模擬價格波動
            const volatility = 50; // 波動幅度
            const trend = (Math.random() - 0.5) * 0.1; // 微小趨勢
            const randomChange = (Math.random() - 0.5) * volatility;
            
            previousPrice = currentPrice;
            currentPrice = Math.max(20000, Math.min(30000, currentPrice + randomChange + trend));
            
            // 添加新數據點
            const now = new Date();
            chartData.push({
                x: now,
                y: currentPrice
            });
            
            // 保持最多100個數據點
            if (chartData.length > 100) {
                chartData.shift();
            }
            
            updateChart();
            updatePriceInfo();
        }

        // 開始演示
        function startDemo() {
            if (isRunning) return;
            
            isRunning = true;
            document.getElementById('demoStatus').textContent = '演示運行中... (模擬實時數據更新)';
            
            // 生成初始數據
            generateInitialData();
            
            // 開始定期更新
            demoInterval = setInterval(generateNewPrice, 3000); // 每3秒更新一次
        }

        // 停止演示
        function stopDemo() {
            if (!isRunning) return;
            
            isRunning = false;
            document.getElementById('demoStatus').textContent = '演示已停止';
            
            if (demoInterval) {
                clearInterval(demoInterval);
                demoInterval = null;
            }
        }

        // 重置演示
        function resetDemo() {
            stopDemo();
            
            // 重置數據
            chartData = [];
            currentPrice = basePrice;
            previousPrice = basePrice;
            
            // 清空圖表
            priceChart.data.datasets[0].data = [];
            priceChart.update();
            
            // 重置顯示
            document.getElementById('currentPrice').textContent = '25,000.00';
            document.getElementById('priceChange').textContent = '+0.00';
            document.getElementById('changePercent').textContent = '+0.00%';
            document.getElementById('volume').textContent = '5,280';
            document.getElementById('lastUpdate').textContent = '--:--:--';
            
            document.getElementById('demoStatus').textContent = '數據已重置，點擊"開始演示"重新開始';
        }

        // 頁面加載完成後初始化
        document.addEventListener('DOMContentLoaded', function() {
            initChart();
        });

        // 頁面卸載時清理
        window.addEventListener('beforeunload', function() {
            stopDemo();
        });
    </script>
</body>
</html>
