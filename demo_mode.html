<!DOCTYPE html>
<html lang="zh-HK">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>恆生指數期貨5分鐘監察圖 - 演示模式</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-chart-financial"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
            color: #333;
        }
        .demo-notice {
            background-color: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border: 1px solid #ffeaa7;
        }
        .demo-notice h4 {
            margin: 0 0 10px 0;
        }
        .connection-panel {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            margin: 5px;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.demo {
            background-color: #cce5ff;
            color: #004085;
            border: 1px solid #99d6ff;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .chart-container {
            position: relative;
            height: 500px;
            margin-top: 20px;
        }
        .info-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .info-card {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .info-card h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .info-card .value {
            font-size: 1.2em;
            font-weight: bold;
            color: #007bff;
        }
        .positive {
            color: #28a745 !important;
        }
        .negative {
            color: #dc3545 !important;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>恆生指數期貨5分鐘監察圖</h1>
            <p>實時監控恆生指數期貨價格走勢 - 演示模式</p>
        </div>

        <div class="demo-notice">
            <h4>🎯 演示模式說明</h4>
            <p>這是一個演示版本，使用模擬數據展示程式功能。在實際使用中，請確保：</p>
            <ul>
                <li>FUTU OpenD程式已安裝並運行</li>
                <li>已登錄FUTU賬戶並有相應的數據權限</li>
                <li>使用真實的API連接參數</li>
            </ul>
        </div>

        <div class="connection-panel">
            <h3>FUTU 賬戶登入 (演示)</h3>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-bottom: 15px;">
                <div>
                    <label>用戶名:</label>
                    <input type="text" id="demoUsername" placeholder="演示用戶名" value="demo_user">
                </div>
                <div>
                    <label>密碼:</label>
                    <input type="password" id="demoPassword" placeholder="演示密碼" value="demo123">
                </div>
            </div>
            <h3>演示控制</h3>
            <button class="btn btn-success" onclick="startDemo()">登入並開始演示</button>
            <button class="btn btn-danger" onclick="stopDemo()">停止演示</button>
            <button class="btn btn-primary" onclick="resetDemo()">重置數據</button>
            <div id="demoStatus" class="status demo">點擊"登入並開始演示"查看效果</div>
        </div>

        <div class="info-panel">
            <div class="info-card">
                <h4>當前價格</h4>
                <div class="value" id="currentPrice">25,000.00</div>
            </div>
            <div class="info-card">
                <h4>漲跌</h4>
                <div class="value" id="priceChange">+0.00</div>
            </div>
            <div class="info-card">
                <h4>漲跌幅</h4>
                <div class="value" id="changePercent">+0.00%</div>
            </div>
            <div class="info-card">
                <h4>成交量</h4>
                <div class="value" id="volume">5,280</div>
            </div>
            <div class="info-card">
                <h4>最後更新</h4>
                <div class="value" id="lastUpdate">--:--:--</div>
            </div>
        </div>

        <div class="chart-container">
            <canvas id="priceChart"></canvas>
        </div>
    </div>

    <script>
        // 全局變量
        let priceChart = null;
        let demoInterval = null;
        let isRunning = false;
        let chartData = [];
        let basePrice = 25000;
        let currentPrice = basePrice;
        let previousPrice = basePrice;

        // 初始化K線圖表
        function initChart() {
            const ctx = document.getElementById('priceChart').getContext('2d');
            priceChart = new Chart(ctx, {
                type: 'candlestick',
                data: {
                    datasets: [{
                        label: '恆生指數期貨 5分鐘K線 (演示數據)',
                        data: [],
                        borderColor: {
                            up: '#26a69a',    // 上漲蠟燭邊框色
                            down: '#ef5350',  // 下跌蠟燭邊框色
                            unchanged: '#999' // 不變蠟燭邊框色
                        },
                        backgroundColor: {
                            up: '#26a69a',    // 上漲蠟燭填充色
                            down: '#ef5350',  // 下跌蠟燭填充色
                            unchanged: '#999' // 不變蠟燭填充色
                        },
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            type: 'time',
                            time: {
                                unit: 'minute',
                                displayFormats: {
                                    minute: 'HH:mm'
                                }
                            },
                            title: {
                                display: true,
                                text: '時間'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: '價格'
                            },
                            ticks: {
                                callback: function(value) {
                                    return value.toLocaleString();
                                }
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false,
                            callbacks: {
                                label: function(context) {
                                    return '價格: ' + context.parsed.y.toLocaleString();
                                }
                            }
                        }
                    },
                    interaction: {
                        mode: 'nearest',
                        axis: 'x',
                        intersect: false
                    }
                }
            });
        }

        // 生成初始K線數據
        function generateInitialData() {
            chartData = [];
            const now = new Date();
            let price = basePrice;

            for (let i = 99; i >= 0; i--) {
                const time = new Date(now.getTime() - i * 5 * 60 * 1000); // 5分鐘間隔

                // 生成開盤價
                const openPrice = price + (Math.random() - 0.5) * 30;

                // 生成收盤價
                const closePrice = openPrice + (Math.random() - 0.5) * 80;

                // 生成最高價和最低價
                const highPrice = Math.max(openPrice, closePrice) + Math.random() * 40;
                const lowPrice = Math.min(openPrice, closePrice) - Math.random() * 40;

                chartData.push({
                    x: time,
                    o: Math.round(openPrice * 100) / 100,
                    h: Math.round(highPrice * 100) / 100,
                    l: Math.round(lowPrice * 100) / 100,
                    c: Math.round(closePrice * 100) / 100
                });

                // 更新價格為收盤價
                price = closePrice;
            }

            if (chartData.length > 0) {
                currentPrice = chartData[chartData.length - 1].c;
                previousPrice = chartData.length > 1 ? chartData[chartData.length - 2].c : currentPrice;
            }

            updateChart();
            updatePriceInfo();
        }

        // 更新圖表
        function updateChart() {
            priceChart.data.datasets[0].data = chartData;
            priceChart.update('none');
        }

        // 更新價格信息
        function updatePriceInfo() {
            const change = currentPrice - previousPrice;
            const changePercent = previousPrice > 0 ? ((change / previousPrice) * 100) : 0;
            const volume = Math.floor(Math.random() * 10000) + 1000;

            document.getElementById('currentPrice').textContent = currentPrice.toLocaleString('zh-HK', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });

            const changeElement = document.getElementById('priceChange');
            const percentElement = document.getElementById('changePercent');
            
            changeElement.textContent = (change >= 0 ? '+' : '') + change.toFixed(2);
            percentElement.textContent = (changePercent >= 0 ? '+' : '') + changePercent.toFixed(2) + '%';
            
            // 設置顏色
            const colorClass = change >= 0 ? 'positive' : 'negative';
            changeElement.className = 'value ' + colorClass;
            percentElement.className = 'value ' + colorClass;

            document.getElementById('volume').textContent = volume.toLocaleString();
            document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString();
        }

        // 生成新的K線數據
        function generateNewPrice() {
            // 模擬價格波動
            const volatility = 50; // 波動幅度
            const trend = (Math.random() - 0.5) * 0.1; // 微小趨勢

            previousPrice = currentPrice;

            // 生成新的K線數據
            const openPrice = currentPrice + (Math.random() - 0.5) * 20;
            const closePrice = Math.max(20000, Math.min(30000, openPrice + (Math.random() - 0.5) * volatility + trend));
            const highPrice = Math.max(openPrice, closePrice) + Math.random() * 30;
            const lowPrice = Math.min(openPrice, closePrice) - Math.random() * 30;

            currentPrice = closePrice;

            // 添加新K線數據點
            const now = new Date();
            chartData.push({
                x: now,
                o: Math.round(openPrice * 100) / 100,
                h: Math.round(highPrice * 100) / 100,
                l: Math.round(lowPrice * 100) / 100,
                c: Math.round(closePrice * 100) / 100
            });

            // 保持最多100個數據點
            if (chartData.length > 100) {
                chartData.shift();
            }

            updateChart();
            updatePriceInfo();
        }

        // 開始演示
        function startDemo() {
            if (isRunning) return;

            // 驗證登入信息
            const username = document.getElementById('demoUsername').value.trim();
            const password = document.getElementById('demoPassword').value.trim();

            if (!username || !password) {
                document.getElementById('demoStatus').textContent = '請輸入用戶名和密碼';
                document.getElementById('demoStatus').className = 'status error';
                return;
            }

            isRunning = true;
            document.getElementById('demoStatus').textContent = `已登入用戶: ${username} | 演示運行中... (模擬5分鐘K線更新)`;
            document.getElementById('demoStatus').className = 'status demo';

            // 生成初始數據
            generateInitialData();

            // 開始定期更新
            demoInterval = setInterval(generateNewPrice, 3000); // 每3秒更新一次
        }

        // 停止演示
        function stopDemo() {
            if (!isRunning) return;
            
            isRunning = false;
            document.getElementById('demoStatus').textContent = '演示已停止';
            
            if (demoInterval) {
                clearInterval(demoInterval);
                demoInterval = null;
            }
        }

        // 重置演示
        function resetDemo() {
            stopDemo();
            
            // 重置數據
            chartData = [];
            currentPrice = basePrice;
            previousPrice = basePrice;
            
            // 清空圖表
            priceChart.data.datasets[0].data = [];
            priceChart.update();
            
            // 重置顯示
            document.getElementById('currentPrice').textContent = '25,000.00';
            document.getElementById('priceChange').textContent = '+0.00';
            document.getElementById('changePercent').textContent = '+0.00%';
            document.getElementById('volume').textContent = '5,280';
            document.getElementById('lastUpdate').textContent = '--:--:--';
            
            document.getElementById('demoStatus').textContent = '數據已重置，點擊"開始演示"重新開始';
        }

        // 頁面加載完成後初始化
        document.addEventListener('DOMContentLoaded', function() {
            initChart();
        });

        // 頁面卸載時清理
        window.addEventListener('beforeunload', function() {
            stopDemo();
        });
    </script>
</body>
</html>
