// FUTU OpenD 連接配置文件
// 請根據您的實際設置修改以下參數

const FutuConfig = {
    // FUTU OpenD 服務器設置
    server: {
        // 服務器地址 - 通常是本地地址
        host: '127.0.0.1',
        
        // WebSocket端口 - FUTU OpenD默認端口
        port: 33333,
        
        // 是否使用SSL - 通常為false
        ssl: false,
        
        // API密鑰 - 如果OpenD設置了密鑰驗證，請在此輸入
        // 留空表示不使用密鑰驗證
        apiKey: null
    },

    // 小型恆生指數期貨設置
    futures: {
        // 市場代碼 - 2表示香港期貨市場
        market: 2,

        // 期貨代碼 - 小型恆生指數主力合約
        code: 'MHIMAIN',

        // 備選合約代碼（可根據需要切換）
        alternativeCodes: [
            'MHI2501',  // 2025年1月合約
            'MHI2502',  // 2025年2月合約
            'MHI2503',  // 2025年3月合約
            'HSIMAIN',  // 恆生指數主力合約
            'HSI2501',  // 恆生指數2025年1月合約
            'HSI2502'   // 恆生指數2025年2月合約
        ]
    },

    // 圖表設置
    chart: {
        // K線類型 - 7表示5分鐘K線
        klType: 7,
        
        // 初始數據數量
        initialDataCount: 100,
        
        // 最大數據點數（用於性能優化）
        maxDataPoints: 200,
        
        // 圖表更新間隔（毫秒）
        updateInterval: 2000,
        
        // 圖表顏色設置
        colors: {
            line: '#007bff',
            background: 'rgba(0, 123, 255, 0.1)',
            positive: '#28a745',
            negative: '#dc3545'
        }
    },

    // 訂閱設置
    subscription: {
        // 訂閱類型列表
        subTypes: [
            1,  // 基本報價
            6   // 5分鐘K線
        ],
        
        // 是否訂閱
        isSubOrUnSub: true,
        
        // 是否註冊推送
        isRegOrUnRegPush: true
    },

    // 顯示設置
    display: {
        // 價格小數位數
        priceDecimals: 2,
        
        // 百分比小數位數
        percentDecimals: 2,
        
        // 時間格式
        timeFormat: 'HH:mm',
        
        // 日期時間格式
        dateTimeFormat: 'YYYY-MM-DD HH:mm:ss'
    },

    // 調試設置
    debug: {
        // 是否啟用控制台日誌
        enableLogging: true,
        
        // 日誌級別 - 'info', 'warn', 'error'
        logLevel: 'info',
        
        // 是否顯示原始數據
        showRawData: false
    }
};

// 導出配置（如果在模塊環境中使用）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FutuConfig;
}

// 全局配置（瀏覽器環境）
if (typeof window !== 'undefined') {
    window.FutuConfig = FutuConfig;
}

// 配置驗證函數
function validateConfig() {
    const errors = [];
    
    // 檢查服務器配置
    if (!FutuConfig.server.host) {
        errors.push('服務器地址不能為空');
    }
    
    if (!FutuConfig.server.port || FutuConfig.server.port < 1 || FutuConfig.server.port > 65535) {
        errors.push('端口號必須在1-65535之間');
    }
    
    // 檢查期貨配置
    if (!FutuConfig.futures.code) {
        errors.push('期貨代碼不能為空');
    }
    
    if (!FutuConfig.futures.market) {
        errors.push('市場代碼不能為空');
    }
    
    // 檢查圖表配置
    if (FutuConfig.chart.maxDataPoints < 10) {
        errors.push('最大數據點數不能少於10');
    }
    
    if (FutuConfig.chart.updateInterval < 1000) {
        errors.push('更新間隔不能少於1秒');
    }
    
    return errors;
}

// 獲取配置的輔助函數
function getServerConfig() {
    return FutuConfig.server;
}

function getFuturesConfig() {
    return FutuConfig.futures;
}

function getChartConfig() {
    return FutuConfig.chart;
}

// 更新配置的輔助函數
function updateServerConfig(newConfig) {
    Object.assign(FutuConfig.server, newConfig);
}

function updateFuturesConfig(newConfig) {
    Object.assign(FutuConfig.futures, newConfig);
}

function updateChartConfig(newConfig) {
    Object.assign(FutuConfig.chart, newConfig);
}

// 重置為默認配置
function resetToDefault() {
    // 這裡可以重置所有配置到默認值
    console.log('配置已重置為默認值');
}

// 保存配置到本地存儲
function saveConfigToLocalStorage() {
    if (typeof localStorage !== 'undefined') {
        localStorage.setItem('futuConfig', JSON.stringify(FutuConfig));
        console.log('配置已保存到本地存儲');
    }
}

// 從本地存儲加載配置
function loadConfigFromLocalStorage() {
    if (typeof localStorage !== 'undefined') {
        const savedConfig = localStorage.getItem('futuConfig');
        if (savedConfig) {
            try {
                const parsedConfig = JSON.parse(savedConfig);
                Object.assign(FutuConfig, parsedConfig);
                console.log('配置已從本地存儲加載');
                return true;
            } catch (error) {
                console.error('加載配置失敗:', error);
                return false;
            }
        }
    }
    return false;
}

// 導出輔助函數
if (typeof window !== 'undefined') {
    window.FutuConfigUtils = {
        validate: validateConfig,
        getServer: getServerConfig,
        getFutures: getFuturesConfig,
        getChart: getChartConfig,
        updateServer: updateServerConfig,
        updateFutures: updateFuturesConfig,
        updateChart: updateChartConfig,
        reset: resetToDefault,
        save: saveConfigToLocalStorage,
        load: loadConfigFromLocalStorage
    };
}
