# 恆生指數期貨5分鐘監察圖 - 使用指南

## 📁 文件說明

您的項目現在包含以下文件：

### 主要程式文件
- **`hsi_futures_monitor.html`** - 主程式，連接真實FUTU OpenD
- **`demo_mode.html`** - 演示版本，使用模擬數據
- **`config.js`** - 配置文件，包含所有設定參數

### 文檔文件
- **`README.md`** - 詳細說明文檔
- **`使用指南.md`** - 本文件，快速上手指南

### API文件夾
- **`FTAPI4JS_9.3.5308/`** - FUTU API JavaScript版本

## 🚀 快速開始

### 方法一：演示模式（推薦新手）

1. **打開演示版本**
   ```
   雙擊 demo_mode.html 或在瀏覽器中打開
   ```

2. **模擬登入**
   - 用戶名：`demo_user`（已預填）
   - 密碼：`demo123`（已預填）
   - 點擊「登入並開始演示」按鈕

3. **開始演示**
   - 觀察實時更新的K線圖表和數據
   - 可以隨時停止或重置

4. **功能體驗**
   - FUTU賬戶登入界面
   - 實時價格更新
   - 5分鐘K線圖表（蠟燭圖）
   - 開盤、收盤、最高、最低價格顯示
   - 漲跌幅顯示
   - 成交量信息

### 方法二：真實連接（需要FUTU OpenD）

1. **準備工作**
   - 確保FUTU OpenD已安裝並運行
   - 確保已登錄FUTU賬戶
   - 確認有恆生指數期貨數據權限

2. **打開主程式**
   ```
   雙擊 hsi_futures_monitor.html 或在瀏覽器中打開
   ```

3. **登入FUTU賬戶**
   - 用戶名：輸入您的FUTU賬戶用戶名
   - 密碼：輸入您的FUTU賬戶密碼

4. **配置連接**
   - 服務器地址：`127.0.0.1`（本地）
   - 端口：`33333`（FUTU OpenD默認端口）
   - API密鑰：如果設置了密鑰則填入，否則留空

5. **開始監控**
   - 點擊「登入並連接」按鈕
   - 等待登入和連接成功
   - 開始接收實時K線數據

## ⚙️ 配置說明

### 修改配置文件 (config.js)

```javascript
// 修改服務器設置
FutuConfig.server.host = '127.0.0.1';     // 服務器地址
FutuConfig.server.port = 33333;           // 端口號
FutuConfig.server.apiKey = 'your_key';    // API密鑰

// 修改期貨合約
FutuConfig.futures.code = 'HSImain';      // 主力合約
// 或者使用具體月份合約
FutuConfig.futures.code = 'HSI2501';      // 2025年1月合約
```

### 常用設置

1. **更改監控合約**
   - 編輯 `config.js` 中的 `FutuConfig.futures.code`
   - 可選值：`HSImain`（主力）、`HSI2501`、`HSI2502` 等

2. **調整更新頻率**
   - 修改 `FutuConfig.chart.updateInterval`
   - 單位：毫秒（建議不少於2000）

3. **修改圖表顏色**
   - 編輯 `FutuConfig.chart.colors` 部分

## 🔧 故障排除

### 常見問題及解決方案

#### 1. 連接失敗
**症狀**：點擊連接後顯示「連接失敗」

**解決方案**：
- ✅ 檢查FUTU OpenD是否正在運行
- ✅ 確認端口號是否正確（默認33333）
- ✅ 檢查防火牆設置
- ✅ 嘗試重啟FUTU OpenD

#### 2. 無數據顯示
**症狀**：連接成功但沒有價格數據

**解決方案**：
- ✅ 確認已登錄FUTU賬戶
- ✅ 檢查是否有恆指期貨數據權限
- ✅ 確認市場是否開放（交易時間）
- ✅ 嘗試更換合約代碼

#### 3. 圖表不更新
**症狀**：有初始數據但不實時更新

**解決方案**：
- ✅ 檢查瀏覽器控制台錯誤（按F12）
- ✅ 確認WebSocket連接穩定
- ✅ 刷新頁面重新連接

#### 4. API密鑰錯誤
**症狀**：提示密鑰驗證失敗

**解決方案**：
- ✅ 檢查FUTU OpenD的密鑰設置
- ✅ 確認輸入的密鑰正確
- ✅ 如果不確定，嘗試留空

## 📊 功能說明

### 主要功能
- **FUTU賬戶登入** - 安全的賬戶驗證系統
- **實時價格監控** - 顯示當前期貨價格
- **5分鐘K線圖** - 專業的蠟燭圖顯示
- **OHLC數據** - 開盤、最高、最低、收盤價格
- **漲跌統計** - 價格變化和百分比
- **成交量顯示** - 當前成交量信息
- **自動更新** - 無需手動刷新

### K線圖表功能
- **蠟燭圖顯示** - 綠色上漲、紅色下跌
- **縮放** - 滾輪縮放時間軸
- **平移** - 拖拽查看歷史數據
- **懸停提示** - 鼠標懸停查看OHLC詳細數值
- **自動滾動** - 新K線數據自動顯示
- **時間軸** - 精確的5分鐘時間間隔

## 🎯 使用技巧

### 最佳實踐
1. **穩定網絡** - 確保網絡連接穩定
2. **定期重啟** - 長時間運行後建議重啟程式
3. **監控資源** - 注意瀏覽器內存使用
4. **備份配置** - 保存自定義配置設置

### 性能優化
1. **數據限制** - 程式自動限制數據點數量
2. **更新頻率** - 可調整更新間隔以平衡性能
3. **瀏覽器選擇** - 推薦使用Chrome或Edge

## 📞 技術支持

### 調試信息
打開瀏覽器開發者工具（F12）查看：
- **Console** - 錯誤和日誌信息
- **Network** - 網絡連接狀態
- **Application** - 本地存儲數據

### 日誌記錄
程式會在控制台輸出詳細日誌：
```
連接建立成功
訂閱成功: {...}
收到推送數據: 3005 {...}
```

### 聯繫方式
如遇到問題，請：
1. 檢查本指南的故障排除部分
2. 查看瀏覽器控制台錯誤信息
3. 確認FUTU OpenD和賬戶狀態
4. 參考FUTU官方API文檔

## 📝 更新日誌

**v1.1** (2025-01-07)
- ✅ 添加FUTU賬戶登入功能
- ✅ 圖表改為專業K線顯示（蠟燭圖）
- ✅ OHLC數據完整顯示
- ✅ 改進的演示模式
- ✅ 更好的用戶界面

**v1.0** (2025-01-07)
- ✅ 初始版本發布
- ✅ 基本連接和數據顯示功能
- ✅ 5分鐘線性圖表
- ✅ 實時價格更新
- ✅ 演示模式
- ✅ 配置文件支持

---

**祝您使用愉快！** 📈
